use crate::config::DwTestItemConfig;
use common::dto::ads::key::test_item_bin_key::TestItemBinKey;
use common::dto::ads::key::test_item_program_key::TestItemProgramKey;
use common::dto::ads::key::test_item_site_bin_key::TestItemSiteBinKey;
use common::dto::ads::key::test_item_site_key::TestItemSiteKey;
use common::dto::ads::value::test_item_bin::TestItemBin;
use common::dto::ads::value::test_item_detail::TestItemDetail;
use common::dto::ads::value::test_item_program::TestItemProgram;
use common::dto::ads::value::test_item_site::TestItemSite;
use common::dto::ads::value::test_item_site_bin::TestItemSiteBin;
use common::dto::ods::product_config::OdsProductConfig;
use common::model::constant::P;
use rayon::prelude::*;
use std::collections::HashMap;
use std::error::Error;
use std::sync::{Arc, Mutex};

/// Grouped test item details for different aggregation levels
/// Contains both total and pass-only groups for performance optimization
#[derive(Debug)]
struct GroupedTestItemDetails {
    /// Groups for TestItemProgram aggregation (by program key) - all records
    program_groups_total: HashMap<TestItemProgramKey, Vec<Arc<TestItemDetail>>>,
    /// Groups for TestItemProgram aggregation (by program key) - pass-only records
    program_groups_pass: HashMap<TestItemProgramKey, Vec<Arc<TestItemDetail>>>,

    /// Groups for TestItemSite aggregation (by site key) - all records
    site_groups_total: HashMap<TestItemSiteKey, Vec<Arc<TestItemDetail>>>,
    /// Groups for TestItemSite aggregation (by site key) - pass-only records
    site_groups_pass: HashMap<TestItemSiteKey, Vec<Arc<TestItemDetail>>>,

    /// Groups for TestItemBin aggregation (by bin key) - all records
    bin_groups_total: HashMap<TestItemBinKey, Vec<Arc<TestItemDetail>>>,
    /// Groups for TestItemBin aggregation (by bin key) - pass-only records
    bin_groups_pass: HashMap<TestItemBinKey, Vec<Arc<TestItemDetail>>>,

    /// Groups for TestItemSiteBin aggregation (by site-bin key) - all records
    site_bin_groups_total: HashMap<TestItemSiteBinKey, Vec<Arc<TestItemDetail>>>,
    /// Groups for TestItemSiteBin aggregation (by site-bin key) - pass-only records
    site_bin_groups_pass: HashMap<TestItemSiteBinKey, Vec<Arc<TestItemDetail>>>,
}

impl GroupedTestItemDetails {
    /// 创建空的分组结果
    fn new() -> Self {
        Self {
            program_groups_total: HashMap::new(),
            program_groups_pass: HashMap::new(),
            site_groups_total: HashMap::new(),
            site_groups_pass: HashMap::new(),
            bin_groups_total: HashMap::new(),
            bin_groups_pass: HashMap::new(),
            site_bin_groups_total: HashMap::new(),
            site_bin_groups_pass: HashMap::new(),
        }
    }
}

/// ADS YMS Test Item Service handles transformation from DWD to ADS layer
/// This service processes test item data for YMS (Yield Management System) analytics
///
/// Corresponds to the ADS layer processing in the original Scala implementation
#[derive(Debug, Clone)]
pub struct AdsYmsTestItemService {
    /// Configuration properties for the service
    properties: DwTestItemConfig,
    /// Test area (CP or FT)
    test_area: String,
}

impl AdsYmsTestItemService {
    pub fn new(properties: DwTestItemConfig, test_area: String) -> Self {
        Self { properties, test_area }
    }

    /// Calculate all ADS test item aggregations with multi-threading support
    ///
    /// This method replicates the Scala calculateAll functionality:
    /// def calculateAll(spark: SparkSession, testItemDetail: Dataset[TestItemDetail],
    ///                  productList: Broadcast[List[OdsProductConfig]]):
    ///                  (Dataset[TestItemProgram], Dataset[TestItemSite], Dataset[TestItemBin], Dataset[TestItemSiteBin])
    ///
    /// Each aggregation type generates TWO records:
    /// - Total: All records aggregated (parameter 0)
    /// - Pass-only: Only passed records aggregated (parameter 1, TEST_RESULT = 1)
    ///
    /// # Arguments
    /// * `test_item_details` - Vector of TestItemDetail records to process
    /// * `product_list` - List of product configurations for product info lookup
    ///
    /// # Returns
    /// A tuple containing (TestItemProgram, TestItemSite, TestItemBin, TestItemSiteBin) vectors
    /// Each vector contains both total and pass-only aggregations
    ///
    /// # Performance
    /// This method uses parallel processing with rayon to improve performance:
    /// - Each aggregation type (Program, Site, Bin, SiteBin) is processed in parallel
    /// - Within each type, total and pass-only groups are processed in parallel
    /// - Individual group aggregations are also parallelized
    pub fn calculate_all(
        &self,
        test_item_details: &Vec<Arc<TestItemDetail>>,
        product_list: &[OdsProductConfig],
    ) -> Result<(Vec<TestItemProgram>, Vec<TestItemSite>, Vec<TestItemBin>, Vec<TestItemSiteBin>), Box<dyn Error>> {
        let grouped_data_vec = self.group_test_item_details(test_item_details)?;

        // 并行处理四种聚合类型
        let ((programs, sites), (bins, site_bins)) = rayon::join(
            || {
                rayon::join(
                    || self.process_program_groups(&grouped_data_vec, product_list),
                    || self.process_site_groups(&grouped_data_vec, product_list),
                )
            },
            || {
                rayon::join(
                    || self.process_bin_groups(&grouped_data_vec, product_list),
                    || self.process_site_bin_groups(&grouped_data_vec, product_list),
                )
            },
        );

        Ok((programs, sites, bins, site_bins))
    }

    /// 处理Program聚合组
    fn process_program_groups(
        &self,
        grouped_data_vec: &Vec<GroupedTestItemDetails>,
        product_list: &[OdsProductConfig],
    ) -> Vec<TestItemProgram> {
        let (total, pass_only) = rayon::join(
            || self.aggregate_program_groups_from_vec(grouped_data_vec, 0, product_list),
            || self.aggregate_program_groups_from_vec(grouped_data_vec, 1, product_list),
        );
        [total, pass_only].concat()
    }

    /// 处理Site聚合组
    fn process_site_groups(
        &self,
        grouped_data_vec: &Vec<GroupedTestItemDetails>,
        product_list: &[OdsProductConfig],
    ) -> Vec<TestItemSite> {
        let (total, pass_only) = rayon::join(
            || self.aggregate_site_groups_from_vec(grouped_data_vec, 0, product_list),
            || self.aggregate_site_groups_from_vec(grouped_data_vec, 1, product_list),
        );
        [total, pass_only].concat()
    }

    /// 处理Bin聚合组
    fn process_bin_groups(
        &self,
        grouped_data_vec: &Vec<GroupedTestItemDetails>,
        product_list: &[OdsProductConfig],
    ) -> Vec<TestItemBin> {
        let (total, pass_only) = rayon::join(
            || self.aggregate_bin_groups_from_vec(grouped_data_vec, 0, product_list),
            || self.aggregate_bin_groups_from_vec(grouped_data_vec, 1, product_list),
        );
        [total, pass_only].concat()
    }

    /// 处理SiteBin聚合组
    fn process_site_bin_groups(
        &self,
        grouped_data_vec: &Vec<GroupedTestItemDetails>,
        product_list: &[OdsProductConfig],
    ) -> Vec<TestItemSiteBin> {
        let (total, pass_only) = rayon::join(
            || self.aggregate_site_bin_groups_from_vec(grouped_data_vec, 0, product_list),
            || self.aggregate_site_bin_groups_from_vec(grouped_data_vec, 1, product_list),
        );
        [total, pass_only].concat()
    }

    /// 从Vec<GroupedTestItemDetails>聚合Program组
    fn aggregate_program_groups_from_vec(
        &self,
        grouped_data_vec: &Vec<GroupedTestItemDetails>,
        is_pass_only: u8,
        product_list: &[OdsProductConfig],
    ) -> Vec<TestItemProgram> {
        grouped_data_vec
            .par_iter()
            .flat_map(|grouped_data| {
                if is_pass_only == 1 { &grouped_data.program_groups_pass } else { &grouped_data.program_groups_total }
                    .par_iter()
                    .map(|(_, items)| TestItemProgram::from_test_items(items, is_pass_only, product_list))
            })
            .collect()
    }

    /// 从Vec<GroupedTestItemDetails>聚合Site组
    fn aggregate_site_groups_from_vec(
        &self,
        grouped_data_vec: &Vec<GroupedTestItemDetails>,
        is_pass_only: u8,
        product_list: &[OdsProductConfig],
    ) -> Vec<TestItemSite> {
        grouped_data_vec
            .par_iter()
            .flat_map(|grouped_data| {
                if is_pass_only == 1 { &grouped_data.site_groups_pass } else { &grouped_data.site_groups_total }
                    .par_iter()
                    .map(|(_, items)| TestItemSite::from_test_items(items, is_pass_only, product_list))
            })
            .collect()
    }

    /// 从Vec<GroupedTestItemDetails>聚合Bin组
    fn aggregate_bin_groups_from_vec(
        &self,
        grouped_data_vec: &Vec<GroupedTestItemDetails>,
        is_pass_only: u8,
        product_list: &[OdsProductConfig],
    ) -> Vec<TestItemBin> {
        grouped_data_vec
            .par_iter()
            .flat_map(|grouped_data| {
                if is_pass_only == 1 { &grouped_data.bin_groups_pass } else { &grouped_data.bin_groups_total }
                    .par_iter()
                    .map(|(_, items)| TestItemBin::from_test_items(items, is_pass_only, product_list))
            })
            .collect()
    }

    /// 从Vec<GroupedTestItemDetails>聚合SiteBin组
    fn aggregate_site_bin_groups_from_vec(
        &self,
        grouped_data_vec: &Vec<GroupedTestItemDetails>,
        is_pass_only: u8,
        product_list: &[OdsProductConfig],
    ) -> Vec<TestItemSiteBin> {
        grouped_data_vec
            .par_iter()
            .flat_map(|grouped_data| {
                if is_pass_only == 1 { &grouped_data.site_bin_groups_pass } else { &grouped_data.site_bin_groups_total }
                    .par_iter()
                    .map(|(_, items)| TestItemSiteBin::from_test_items(items, is_pass_only, product_list))
            })
            .collect()
    }

    /// Group test item details by different aggregation keys (多线程版本)
    fn group_test_item_details(
        &self,
        test_item_details: &Vec<Arc<TestItemDetail>>,
    ) -> Result<Vec<GroupedTestItemDetails>, Box<dyn Error>> {
        // 并行分组处理
        let chunk_size = self.properties.get_batch_size()?;

        let partial_results: Vec<GroupedTestItemDetails> = test_item_details
            .par_chunks(chunk_size)
            .map(|chunk| {
                self.group_test_item_details_sequential(chunk).unwrap_or_else(|_| GroupedTestItemDetails::new())
            })
            .collect();

        Ok(partial_results)
    }

    /// 单线程版本的分组方法（用于并行处理的基础）
    fn group_test_item_details_sequential(
        &self,
        test_item_details: &[Arc<TestItemDetail>],
    ) -> Result<GroupedTestItemDetails, Box<dyn Error>> {
        let mut program_groups_total: HashMap<TestItemProgramKey, Vec<Arc<TestItemDetail>>> = HashMap::new();
        let mut program_groups_pass: HashMap<TestItemProgramKey, Vec<Arc<TestItemDetail>>> = HashMap::new();
        let mut site_groups_total: HashMap<TestItemSiteKey, Vec<Arc<TestItemDetail>>> = HashMap::new();
        let mut site_groups_pass: HashMap<TestItemSiteKey, Vec<Arc<TestItemDetail>>> = HashMap::new();
        let mut bin_groups_total: HashMap<TestItemBinKey, Vec<Arc<TestItemDetail>>> = HashMap::new();
        let mut bin_groups_pass: HashMap<TestItemBinKey, Vec<Arc<TestItemDetail>>> = HashMap::new();
        let mut site_bin_groups_total: HashMap<TestItemSiteBinKey, Vec<Arc<TestItemDetail>>> = HashMap::new();
        let mut site_bin_groups_pass: HashMap<TestItemSiteBinKey, Vec<Arc<TestItemDetail>>> = HashMap::new();

        for detail in test_item_details {
            // Create keys for each aggregation level
            let program_key = TestItemProgramKey::from_test_item_detail(&detail);
            let site_key = TestItemSiteKey::from_test_item_detail(&detail);
            let bin_key = TestItemBinKey::from_test_item_detail(&detail);
            let site_bin_key = TestItemSiteBinKey::from_test_item_detail(&detail);

            // Check if this is a pass record
            let is_pass = detail.HBIN_PF.as_ref() == P;

            // Add to pass-only groups if this is a pass record
            if is_pass {
                program_groups_pass.entry(program_key.clone()).or_insert_with(Vec::new).push(detail.clone());
                site_groups_pass.entry(site_key.clone()).or_insert_with(Vec::new).push(detail.clone());
                bin_groups_pass.entry(bin_key.clone()).or_insert_with(Vec::new).push(detail.clone());
                site_bin_groups_pass.entry(site_bin_key.clone()).or_insert_with(Vec::new).push(detail.clone());
            }

            // Add to total groups (all records)
            program_groups_total.entry(program_key).or_insert_with(Vec::new).push(detail.clone());
            site_groups_total.entry(site_key).or_insert_with(Vec::new).push(detail.clone());
            bin_groups_total.entry(bin_key).or_insert_with(Vec::new).push(detail.clone());
            site_bin_groups_total.entry(site_bin_key).or_insert_with(Vec::new).push(detail.clone());
        }

        Ok(GroupedTestItemDetails {
            program_groups_total,
            program_groups_pass,
            site_groups_total,
            site_groups_pass,
            bin_groups_total,
            bin_groups_pass,
            site_bin_groups_total,
            site_bin_groups_pass,
        })
    }
}
